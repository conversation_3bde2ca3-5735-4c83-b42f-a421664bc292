{
  "root": true,
  "ignorePatterns": [
    "projects/**/*",
    "src/app/shared/icons/**/*"
  ],
  "plugins": [
    "@typescript-eslint",
    "prettier",
    "@stylistic",
    "@ngrx"
  ],
  "parserOptions": {
    "ecmaVersion": "latest",
    "sourceType": "module"
  },
  "parser": "@typescript-eslint/parser",
  "overrides": [
    {
      "files": [
        "src/**/*.ts"
      ],
      "extends": [
        "eslint:recommended",
        "plugin:@typescript-eslint/recommended",
        "plugin:@angular-eslint/recommended",
        "plugin:@angular-eslint/template/process-inline-templates",
        "plugin:rxjs/recommended",
        "plugin:prettier/recommended",
        "plugin:@ngrx/all"
      ],
      "rules": {
        "@typescript-eslint/no-unused-vars": [
          "error",
          {
            "varsIgnorePattern": "^_",
            "argsIgnorePattern": "^_"
          }
        ],
        "@typescript-eslint/explicit-member-accessibility": [
          "error",
          {
            "accessibility": "explicit",
            "overrides": {
              "constructors": "no-public"
            }
          }
        ],
        "@typescript-eslint/explicit-module-boundary-types": "error",
        "@typescript-eslint/prefer-readonly": "error",
        "@stylistic/lines-between-class-members": [
          "error",
          "always"
        ],
        "@angular-eslint/directive-selector": [
          "error",
          {
            "type": "attribute",
            "prefix": "fish",
            "style": "camelCase"
          }
        ],
        "@angular-eslint/component-selector": [
          "error",
          {
            "type": "element",
            "prefix": "fish",
            "style": "kebab-case"
          }
        ],
        "rxjs/finnish": [
          "error",
          {
            "functions": true,
            "methods": true,
            "names": {
              "^(canActivate|canActivateChild|canDeactivate|canLoad|intercept|resolve|validate|getTranslation|transform)$": false
            },
            "parameters": true,
            "properties": true,
            "strict": false,
            "types": {
              "^EventEmitter$": false
            },
            "variables": true
          }
        ]
      }
    },
    // NOTE: WE ARE NOT APPLYING PRETTIER IN THIS OVERRIDE, ONLY @ANGULAR-ESLINT/TEMPLATE
    {
      "files": [
        "src/**/*.html"
      ],
      "extends": [
        "plugin:@angular-eslint/template/recommended",
        "plugin:@angular-eslint/template/accessibility"
      ]
    },
    // NOTE: WE ARE NOT APPLYING @ANGULAR-ESLINT/TEMPLATE IN THIS OVERRIDE, ONLY PRETTIER
    {
      "files": [
        "*.html"
      ],
      "excludedFiles": [
        "*inline-template-*.component.html"
      ],
      "extends": [
        "plugin:prettier/recommended"
      ],
      "rules": {
        // NOTE: WE ARE OVERRIDING THE DEFAULT CONFIG TO ALWAYS SET THE PARSER TO ANGULAR (SEE BELOW)
        "prettier/prettier": [
          "error",
          {
            "parser": "angular"
          }
        ]
      }
    }
  ]
}
