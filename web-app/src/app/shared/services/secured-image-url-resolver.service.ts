import {HttpClient} from '@angular/common/http';
import {Injectable, OnDestroy} from '@angular/core';
import {DomSanitizer, SafeUrl} from '@angular/platform-browser';

import {catchError, map, Observable, of, shareReplay} from 'rxjs';

interface CacheEntry {
  url: string;
  safeUrl: SafeUrl;
  lastAccessed: number;
  refCount: number; // Track how many components are using this image
}

@Injectable({
  providedIn: 'root',
})
export class SecuredImageUrlResolverService implements OnDestroy {
  private readonly urlCache = new Map<string, CacheEntry>();
  private readonly maxCacheSize = 50;
  private readonly maxAge = 30 * 60 * 1000; // 30 minutes

  constructor(
    private readonly http: HttpClient,
    private readonly sanitizer: DomSanitizer
  ) {
  }

  public ngOnDestroy(): void {
    this.clearCache();
  }

  public getObjectUrl$(src: string): Observable<SafeUrl> {
    const cachedEntry = this.urlCache.get(src);
    if (cachedEntry) {
      // Update last accessed time and increment reference count
      cachedEntry.lastAccessed = Date.now();
      cachedEntry.refCount++;
      return of(cachedEntry.safeUrl);
    }

    return this.http.get(src, {responseType: 'blob', observe: 'response'}).pipe(
      map((response) => {
        const blob = response.body;
        if (!blob) {
          throw new Error('Failed to load image');
        }
        const url = URL.createObjectURL(blob);
        const sanitizedUrl = this.sanitizer.bypassSecurityTrustUrl(url);

        if (!sanitizedUrl) {
          // Clean up blob URL if sanitization fails
          URL.revokeObjectURL(url);
          throw new Error('Failed to sanitize URL');
        }
        
        this.addToCache(src, url, sanitizedUrl);

        return sanitizedUrl;
      }),
      shareReplay({bufferSize: 1, refCount: true}),
      catchError((e) => {
        throw new Error('Failed to load image: ' + e);
      })
    );
  }

  /**
   * Decrement reference count for an image (called by pipes when they're destroyed)
   */
  public decrementRefCount(src: string): void {
    const entry = this.urlCache.get(src);
    if (entry) {
      entry.refCount = Math.max(0, entry.refCount - 1);

      // If no more references and entry is old, clean it up
      if (entry.refCount === 0 && Date.now() - entry.lastAccessed > 60000) { // 1 minute grace period
        this.revokeUrl(src);
      }
    }
  }

  /**
   * Manually clear the entire cache and revoke all blob URLs
   */
  public clearCache(): void {
    for (const entry of this.urlCache.values()) {
      URL.revokeObjectURL(entry.url);
    }
    this.urlCache.clear();
  }

  /**
   * Remove a specific entry from cache
   */
  public revokeUrl(src: string): void {
    const cached = this.urlCache.get(src);
    if (cached) {
      URL.revokeObjectURL(cached.url);
      this.urlCache.delete(src);
    }
  }

  /**
   * Clean up expired entries based on age
   */
  public cleanupExpiredEntries(): void {
    const now = Date.now();
    const expiredKeys: string[] = [];

    for (const [key, entry] of this.urlCache.entries()) {
      if (now - entry.lastAccessed > this.maxAge) {
        expiredKeys.push(key);
      }
    }

    for (const key of expiredKeys) {
      this.revokeUrl(key);
    }
  }

  /**
   * Get current cache size
   */
  public getCacheSize(): number {
    return this.urlCache.size;
  }

  private addToCache(src: string, url: string, safeUrl: SafeUrl): void {
    // Clean up expired entries first
    this.cleanupExpiredEntries();

    // If cache is at max size, remove least recently used entry
    if (this.urlCache.size >= this.maxCacheSize) {
      this.evictLeastRecentlyUsed();
    }

    this.urlCache.set(src, {
      url,
      safeUrl,
      lastAccessed: Date.now(),
      refCount: 1
    });
  }

  private evictLeastRecentlyUsed(): void {
    let oldestKey: string | null = null;
    let oldestTime = Date.now();

    for (const [key, entry] of this.urlCache.entries()) {
      if (entry.lastAccessed < oldestTime) {
        oldestTime = entry.lastAccessed;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.revokeUrl(oldestKey);
    }
  }
}
