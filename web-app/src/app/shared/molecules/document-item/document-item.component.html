<fish-payment-item [class]="'min-h-16'">
  <fish-payment-item-main-area [class]="'px-2'" [hasCheckbox]="false">
    <div class="flex w-full items-center">
      <div class="flex items-center gap-4">
        <fish-icon-document-pdf size="48"></fish-icon-document-pdf>
        <div class="flex-col text-s leading-5">
          <div [innerText]="'document_item.type.pdf' | translate"></div>
          <div class="font-bold" [innerText]="documentTitle()"></div>
        </div>
      </div>
      <div class="grow"></div>
      <div class="mr-4 flex items-center gap-4">
        @if (documentFederalState()) {
          <div class="mr-8 rounded border-b border-background bg-action-secondary-glass px-4 py-0.5 font-bold shadow-glass-white-tint">
            <span [innerText]="documentFederalState()"></span>
          </div>
        }
        <div class="flex-col text-s leading-5 text-font-secondary">
          <div [innerText]="'document_item.valid_from' | translate"></div>
          <div class="font-bold" [innerText]="document().validFrom | date: 'dd.MM.yyyy'"></div>
        </div>
        @if (showValidTo()) {
          <div class="flex-col text-s leading-5 text-font-secondary">
            <div [innerText]="'document_item.valid_to' | translate"></div>
            <div class="font-bold" [innerText]="(document().validTo | date: 'dd.MM.yyyy') || '—'"></div>
          </div>
        }
      </div>
    </div>
  </fish-payment-item-main-area>
  <fish-payment-item-action-area [class]="'px-4'">
    <div class="flex gap-1">
      <fish-button
        [class]="'w-36'"
        type="secondary"
        (clicked)="printDocumentButtonClicked.emit(document())"
        [disabled]="isFetchingDocument()"
        size="m"
        data-testid="documents-print-button"
      >
        <fish-icon-print size="32" icon></fish-icon-print>
        <span [innerText]="'common.button.print' | translate"></span>
      </fish-button>
      @if (showSendButton()) {
        <fish-button
          [class]="'w-36'"
          type="secondary"
          (clicked)="sendDocumentButtonClicked.emit(document())"
          size="m"
          data-testid="documents-send-button"
        >
          <fish-icon-send size="32" icon></fish-icon-send>
          <span [innerText]="'common.button.send' | translate"></span>
        </fish-button>
      }
    </div>
  </fish-payment-item-action-area>
</fish-payment-item>
