import { Component, Input } from '@angular/core';

import { ResolveSecuredImageUrlPipe } from '../pipes/resolve-secured-image-url.pipe';
import { SecuredImageUrlResolverService } from '../services/secured-image-url-resolver.service';

/**
 * Example component demonstrating automatic memory-safe image loading
 *
 * The pipe now handles all cleanup automatically - no manual management needed!
 */
@Component({
  selector: 'fish-memory-safe-image',
  template: `
    <!-- Simple usage - pipe handles all cleanup automatically -->
    <img
      [src]="imageUrl | resolveSecuredImageUrl"
      [alt]="altText"
      class="max-w-full h-auto"
    />

    <!-- Cache statistics (for debugging) -->
    <div class="text-sm text-gray-500 mt-2" *ngIf="showStats">
      Cache: {{ getCacheSize() }}/50 images
    </div>
  `,
  imports: [
    ResolveSecuredImageUrlPipe
  ],
})
export class MemorySafeImageComponent {
  @Input() imageUrl!: string;
  @Input() altText = '';
  @Input() showStats = false;

  constructor(
    private readonly securedImageService: SecuredImageUrlResolverService
  ) {}

  getCacheSize(): number {
    return this.securedImageService.getCacheSize();
  }
}
