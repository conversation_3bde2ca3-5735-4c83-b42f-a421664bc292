import { ChangeDetectorRef, <PERSON><PERSON><PERSON>roy, <PERSON><PERSON>, PipeTransform } from '@angular/core';
import { SafeUrl } from '@angular/platform-browser';

import { BehaviorSubject, Subscription } from 'rxjs';
import { distinctUntilChanged, filter, switchMap } from 'rxjs/operators';

import { SecuredImageUrlResolverService } from '@/app/shared/services/secured-image-url-resolver.service';

@Pipe({
  name: 'resolveSecuredImageUrl',
  pure: false, // Required for automatic cleanup
})
export class ResolveSecuredImageUrlPipe implements PipeTransform, OnDestroy {
  private readonly subscription = new Subscription();

  private readonly transformValue$ = new BehaviorSubject<string>('');

  private latestValue: SafeUrl = '';

  private currentImageUrl: string = '';

  constructor(
    private readonly cdr: ChangeDetectorRef,
    private readonly securedImageUrlResolverService: SecuredImageUrlResolverService
  ) {
    this.setUpSubscription();
  }

  public transform(fileId: string): SafeUrl {
    if (!fileId) {
      this.cleanupCurrentImage();
      this.latestValue = '';
      return '';
    }

    // Only update if the URL actually changed
    if (fileId !== this.currentImageUrl) {
      this.cleanupCurrentImage();
      this.currentImageUrl = fileId;
      this.transformValue$.next(fileId);
    }

    return this.latestValue;
  }

  public ngOnDestroy(): void {
    this.cleanupCurrentImage();
    this.subscription.unsubscribe();
    this.transformValue$.complete();
  }

  private setUpSubscription(): void {
    const transformSubscription = this.transformValue$
      .asObservable()
      .pipe(
        filter((fileId): fileId is string => !!fileId),
        distinctUntilChanged(),
        switchMap((fileUrl: string) => this.securedImageUrlResolverService.getObjectUrl$(fileUrl))
      )
      .subscribe((value: SafeUrl) => {
        this.latestValue = value;
        this.cdr.markForCheck();
      });

    this.subscription.add(transformSubscription);
  }

  private cleanupCurrentImage(): void {
    if (this.currentImageUrl) {
      // Decrement reference count - service will clean up if no more references
      this.securedImageUrlResolverService.decrementRefCount(this.currentImageUrl);
      this.currentImageUrl = '';
    }
  }
}
