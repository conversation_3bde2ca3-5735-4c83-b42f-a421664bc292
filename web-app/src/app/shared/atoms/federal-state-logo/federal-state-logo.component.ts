import { ChangeDetectionStrategy, Component } from '@angular/core';

import { TranslateModule } from '@ngx-translate/core';

import { ResolveSecuredImageUrlPipe } from '@/app/shared/pipes/resolve-secured-image-url.pipe';

@Component({
  selector: 'fish-federal-state-logo',
  templateUrl: './federal-state-logo.component.html',
  imports: [TranslateModule, ResolveSecuredImageUrlPipe],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FederalStateLogoComponent {}
