import { ChangeDetectionStrategy, Component, Input, inject } from '@angular/core';
import { RouterModule } from '@angular/router';

import { FileDownloadService } from '@/app/shared/services/file-download.service';

@Component({
  templateUrl: './linkbox-link.component.html',
  selector: 'fish-linkbox-link',
  imports: [RouterModule],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LinkboxLinkComponent {
  @Input() public href: string = '#';

  @Input() public disabled: boolean = false;

  private readonly fileDownloadService = inject(FileDownloadService);

  protected onClick(): void {
    this.fileDownloadService.openPdfInNewTab(this.href);
  }
}
