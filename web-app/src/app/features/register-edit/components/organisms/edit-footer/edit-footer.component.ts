import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output } from '@angular/core';
import { FormControl } from '@angular/forms';

import { TranslateModule } from '@ngx-translate/core';
import { twMerge } from 'tailwind-merge';

import { ButtonComponent } from '@/app/shared/atoms/button/button.component';
import { IconArrowLeftComponent } from '@/app/shared/icons/arrow-left/arrow-left.component';
import { IconArrowRightComponent } from '@/app/shared/icons/arrow-right/arrow-right.component';
import { IconCheckComponent } from '@/app/shared/icons/check/check.component';
import { ConfirmBoxComponent } from '@/app/shared/molecules/confirm-box/confirm-box.component';

@Component({
  selector: 'fish-edit-footer',
  imports: [ButtonComponent, ConfirmBoxComponent, TranslateModule, CommonModule, IconArrowRightComponent, IconCheckComponent, IconArrowLeftComponent],
  templateUrl: './edit-footer.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EditFooterComponent {
  @Input() public showGdpr: boolean = false;

  @Input() public thirdPartySubmissionControl?: FormControl;

  @Input() public showBackButton: boolean = false;

  @Input() public isLastStep: boolean = false;

  @Input() public isLoading: boolean = false;

  @Input() public continueButtonLabel?: string;

  @Output() public readonly gdprChanged = new EventEmitter<void>();

  @Output() public readonly backed = new EventEmitter<void>();

  @Output() public readonly continued = new EventEmitter<void>();

  protected get containerClasses(): string {
    // This ensures that the submit button is always on the right, regardless of what is shown on the left side
    return twMerge(['w-full flex items-center h-16', this.showBackButton || this.thirdPartySubmissionControl ? 'justify-between' : 'justify-end']);
  }
}
