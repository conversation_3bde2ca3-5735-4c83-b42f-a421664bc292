<div class="flex flex-col items-stretch justify-between gap-12">
  <div class="flex flex-row items-stretch gap-6">
    <div class="flex-1">
      @if (isTaxReadonly()) {
        <fish-readonly-payment-box
          [licenseType]="licenseType()"
          [preventLeaving]="true"
          [feeCost]="feeCost()"
          [selectedValidityPeriod]="selectedValidityPeriod()"
          [showFee]="showFee()"
          class="block h-full"
        />
      } @else {
        <fish-payment-box
          [licenseType]="licenseType()"
          [preventLeaving]="true"
          [isTaxCheckboxDisabled]="isTaxCheckboxDisabled()"
          [feeCost]="feeCost()"
          [isTaxOptional]="isTaxOptional()"
          [previouslyPaidTax]="previouslyPaid()"
          [showFee]="showFee()"
          [showPreviouslyPayedTax]="showPreviouslyPaidTax()"
          [taxCostOptions]="taxCostOptions()"
          [licenseValidTo]="selectedValidityPeriod()?.validTo"
          class="block h-full"
        />
      }
    </div>
    <div class="w-1/3">
      <fish-payment-method-card
        [isRequired]="isPaymentMethodRequired()"
        [preventLeaving]="true"
        [enabledPaymentTypes]="enabledPaymentTypes()"
        class="block h-full"
      />
    </div>
  </div>
  <fish-edit-footer
    (backed)="backButtonClicked.emit()"
    (continued)="onContinue()"
    [isLastStep]="isLastStep()"
    [continueButtonLabel]="continueButtonLabel()"
    [isLoading]="isLoading()"
    [showBackButton]="showBackButton()"
  />
</div>
