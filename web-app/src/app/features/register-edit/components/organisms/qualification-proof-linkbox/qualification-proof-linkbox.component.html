<fish-linkbox
  [alternativeText]="'edit_form.qualification_proof.help.alternative_text' | translate"
  [header]="'edit_form.qualification_proof.help.header' | translate"
  direction="vertical"
>
  <ng-container *ngIf="federalStateId()">
    <fish-linkbox-link [href]="templateFileUrl() ?? '#'">
      <fish-icon-link icon size="48" data-testid="qualification-proof-help-template" />
      {{ 'edit_form.qualification_proof.help.template_link_text' | translate }}
    </fish-linkbox-link>
    <fish-linkbox-link [href]="antiforgeryFileUrl() ?? '#'">
      <fish-icon-document-pdf icon size="48" data-testid="qualification-proof-help-antiforgery" />
      {{ 'edit_form.qualification_proof.help.antiforgery_link_text' | translate }}
    </fish-linkbox-link>
  </ng-container>
</fish-linkbox>
