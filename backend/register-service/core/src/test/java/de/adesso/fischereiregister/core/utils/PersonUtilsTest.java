package de.adesso.fischereiregister.core.utils;

import de.adesso.fischereiregister.core.model.Birthdate;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.testutils.DomainTestData;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class PersonUtilsTest {
    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    @DisplayName("PersonUtils.matches is successful when the requested is exactly the same.")
    public void testPersonMatchesWhenPersonTheSame(boolean reverseOrdering) {
        // GIVEN
        final Person person = DomainTestData.createPersonWithAddress();
        final Person samePerson = DomainTestData.createPersonWithAddress();

        // WHEN
        final boolean result = reverseOrdering ? PersonUtils.matches(samePerson, person) : PersonUtils.matches(person, samePerson);

        // THEN
        assertTrue(result);
    }

    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    @DisplayName("PersonUtils.matches is successful when address of person is different.")
    public void testPersonMatchesWhenAddressDoesNotMatch(boolean reverseOrdering) {
        // GIVEN
        final Person person = DomainTestData.createPersonWithAddress();
        final Person personWithOtherAddress = DomainTestData.createPersonWithAddress();
        personWithOtherAddress.getAddress().setStreet("otherstreet");

        // WHEN
        final boolean result = reverseOrdering ? PersonUtils.matches(personWithOtherAddress, person) : PersonUtils.matches(person, personWithOtherAddress);

        // THEN
        assertTrue(result);
    }

    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    @DisplayName("PersonUtils.matches is false when firstname mismatches")
    public void testPersonMismatchOnFirstnameMismatch(boolean reverseOrdering) {
        // GIVEN
        final Person person = DomainTestData.createPerson();
        final Person otherPerson = DomainTestData.createPerson();

        otherPerson.setFirstname("othername");

        // WHEN
        final boolean result = reverseOrdering ? PersonUtils.matches(otherPerson, person) : PersonUtils.matches(person, otherPerson);

        // THEN
        assertFalse(result);
    }

    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    @DisplayName("PersonUtils.matches is false when lastname mismatches")
    public void testPersonMismatchOnLastnameMismatch(boolean reverseOrdering) {
        // GIVEN
        final Person person = DomainTestData.createPerson();
        final Person otherPerson = DomainTestData.createPerson();

        otherPerson.setLastname("othername");

        // WHEN
        final boolean result = reverseOrdering ? PersonUtils.matches(otherPerson, person) : PersonUtils.matches(person, otherPerson);

        // THEN
        assertFalse(result);
    }

    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    @DisplayName("PersonUtils.matches is false when lastname mismatches")
    public void testPersonMismatchOnBirthnameMismatch(boolean reverseOrdering) {
        // GIVEN
        final Person person = DomainTestData.createPerson();
        final Person otherPerson = DomainTestData.createPerson();

        otherPerson.setBirthname("othername");

        // WHEN
        final boolean result = reverseOrdering ? PersonUtils.matches(otherPerson, person) : PersonUtils.matches(person, otherPerson);

        // THEN
        assertFalse(result);
    }

    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    @DisplayName("PersonUtils.matches is false when birthplace mismatches")
    public void testPersonMismatchOnBirthplaceMismatch(boolean reverseOrdering) {
        // GIVEN
        final Person person = DomainTestData.createPerson();
        final Person otherPerson = DomainTestData.createPerson();

        otherPerson.setBirthplace("otherplace");

        // WHEN
        final boolean result = reverseOrdering ? PersonUtils.matches(otherPerson, person) : PersonUtils.matches(person, otherPerson);

        // THEN
        assertFalse(result);
    }

    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    @DisplayName("PersonUtils.matches is false when birthdate mismatches")
    public void testPersonMismatchOnBirthdateMismatch(boolean reverseOrdering) {
        // GIVEN
        final Person person = DomainTestData.createPerson();
        final Person otherPerson = DomainTestData.createPerson();

        otherPerson.setBirthdate(new Birthdate(2001, 1, 1));

        // WHEN
        final boolean result = reverseOrdering ? PersonUtils.matches(otherPerson, person) : PersonUtils.matches(person, otherPerson);

        // THEN
        assertFalse(result);
    }

    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    @DisplayName("PersonUtils.matches is false when title mismatches")
    public void testPersonMismatchOnTitleMismatch(boolean reverseOrdering) {
        // GIVEN
        final Person person = DomainTestData.createPerson();
        final Person otherPerson = DomainTestData.createPerson();

        otherPerson.setTitle("");

        // WHEN
        final boolean result = reverseOrdering ? PersonUtils.matches(otherPerson, person) : PersonUtils.matches(person, otherPerson);

        // THEN
        assertFalse(result);
    }

    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    @DisplayName("PersonUtils.matches is false when nationality mismatches")
    public void testPersonMismatchOnNationalityMismatch(boolean reverseOrdering) {
        // GIVEN
        final Person person = DomainTestData.createPerson();
        final Person otherPerson = DomainTestData.createPerson();

        otherPerson.setNationality("Kolumbianisch");

        // WHEN
        final boolean result = reverseOrdering ? PersonUtils.matches(otherPerson, person) : PersonUtils.matches(person, otherPerson);

        // THEN
        assertFalse(result);
    }

    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    @DisplayName("PersonUtils.matches is successful when either nationality is unset.")
    public void testPersonMatchesWhenNationalityUnset(boolean reverseOrdering) {
        // GIVEN
        final Person person = DomainTestData.createPerson();
        final Person otherPerson = DomainTestData.createPerson();

        otherPerson.setNationality(null);

        // WHEN
        final boolean result = reverseOrdering ? PersonUtils.matches(otherPerson, person) : PersonUtils.matches(person, otherPerson);

        // THEN
        assertTrue(result);
    }

    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    @DisplayName("PersonUtils.matches is not differentiating between null or empty strings.")
    public void testPersonMatchesForNullOrEmptyStrings(boolean reverseOrdering) {
        // GIVEN
        final Person person = DomainTestData.createPerson();
        final Person otherPerson = DomainTestData.createPerson();

        person.setNationality(null);
        otherPerson.setNationality("");

        person.setTitle(null);
        otherPerson.setTitle("");

        // WHEN
        final boolean result = reverseOrdering ? PersonUtils.matches(otherPerson, person) : PersonUtils.matches(person, otherPerson);

        // THEN
        assertTrue(result);
    }

    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    @DisplayName("PersonUtils.matches is considering the lastname as birthname, when no birthname was given.")
    public void testPersonMatchesBirthnameWhenOnlyLastnameGiven(boolean reverseOrdering) {
        // GIVEN
        final Person person = DomainTestData.createPerson();
        final Person otherPerson = DomainTestData.createPerson();

        person.setBirthname(person.getLastname());
        otherPerson.setBirthname(null);

        // WHEN
        final boolean result = reverseOrdering ? PersonUtils.matches(otherPerson, person) : PersonUtils.matches(person, otherPerson);

        // THEN
        assertTrue(result);
    }

    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    @DisplayName("PersonUtils.matches is considering the lastname as birthname, when empty birthname was given.")
    public void testPersonMatchesBirthnameWhenEmptyBirthnameGiven(boolean reverseOrdering) {
        // GIVEN
        final Person person = DomainTestData.createPerson();
        final Person otherPerson = DomainTestData.createPerson();

        person.setBirthname(person.getLastname());
        otherPerson.setBirthname("");

        // WHEN
        final boolean result = reverseOrdering ? PersonUtils.matches(otherPerson, person) : PersonUtils.matches(person, otherPerson);

        // THEN
        assertTrue(result);
    }
}
